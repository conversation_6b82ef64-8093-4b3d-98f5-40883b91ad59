import json
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple

from sqlalchemy.orm import Session

from ..models import <PERSON>ch, Image, Sample, SampleMode
from ..schemas import BatchCreate, SampleCreate
from .batch_service import BatchService
from .image_service import ImageService
from .sample_service import SampleService


class ImportService:
    """数据导入服务"""

    def __init__(self, db: Session):
        self.db = db
        self.image_service = ImageService(db)
        self.sample_service = SampleService(db)
        self.batch_service = BatchService(db)

    def parse_import_json(self, json_file_path: Path) -> Dict[str, Any]:
        """
        解析导入的JSON文件

        Args:
            json_file_path: JSON文件路径

        Returns:
            Dict: 解析后的数据

        Raises:
            ValueError: 如果JSON格式无效
        """
        try:
            with open(json_file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            return data
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON格式错误: {e}")
        except FileNotFoundError:
            raise ValueError(f"文件不存在: {json_file_path}")

    def validate_import_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证导入数据格式

        预期格式:
        {
            "batch_name": "批次名称",
            "batch_description": "批次描述",
            "samples": [
                {
                    "image_path": "图片相对路径",
                    "mode": "样本模式",
                    "metadata": {...},
                    "labels": [...]
                },
                ...
            ]
        }

        Returns:
            Tuple[bool, List[str]]: (是否有效, 错误信息列表)
        """
        errors = []

        # 检查顶级必需字段
        required_keys = {"batch_name", "samples"}
        missing_keys = required_keys - set(data.keys())
        if missing_keys:
            errors.append(f"缺少必需字段: {', '.join(missing_keys)}")
            return False, errors

        # 检查batch_name类型
        if not isinstance(data["batch_name"], str):
            errors.append("batch_name 必须是字符串类型")
        elif not data["batch_name"].strip():
            errors.append("batch_name 不能为空")

        # 检查samples字段
        if not isinstance(data["samples"], list):
            errors.append("samples 必须是数组类型")
            return False, errors

        if len(data["samples"]) == 0:
            errors.append("samples 数组不能为空")

        # 验证每个样本
        for i, sample in enumerate(data["samples"]):
            sample_errors = self._validate_single_sample(sample, i + 1)
            errors.extend(sample_errors)

        return len(errors) == 0, errors

    def _validate_single_sample(self, sample: Any, sample_index: int) -> List[str]:
        """验证单个样本的格式"""
        errors = []
        prefix = f"样本 {sample_index}"

        # 检查样本是否为字典
        if not isinstance(sample, dict):
            errors.append(f"{prefix}: 必须是对象类型")
            return errors

        # 检查必需字段
        sample_required_keys = {"image_path", "mode", "metadata"}
        missing_keys = sample_required_keys - set(sample.keys())
        if missing_keys:
            errors.append(f"{prefix}: 缺少必需字段 {', '.join(missing_keys)}")

        # 验证image_path
        if "image_path" in sample:
            if not isinstance(sample["image_path"], str):
                errors.append(f"{prefix}: image_path 必须是字符串")
            elif not sample["image_path"].strip():
                errors.append(f"{prefix}: image_path 不能为空")

        # 验证mode
        if "mode" in sample:
            if not isinstance(sample["mode"], str):
                errors.append(f"{prefix}: mode 必须是字符串")
            else:
                try:
                    mode = SampleMode(sample["mode"])
                    # 如果mode有效，验证对应的metadata
                    if "metadata" in sample:
                        metadata_errors = self._validate_sample_metadata(
                            mode, sample["metadata"], sample_index
                        )
                        errors.extend(metadata_errors)
                except ValueError:
                    valid_modes = [mode.value for mode in SampleMode]
                    errors.append(
                        f"{prefix}: 无效的模式 '{sample['mode']}'，"
                        f"有效模式: {', '.join(valid_modes)}"
                    )

        # 验证metadata
        if "metadata" in sample and not isinstance(sample["metadata"], dict):
            errors.append(f"{prefix}: metadata 必须是对象类型")

        # 验证labels（可选字段）
        if "labels" in sample:
            if not isinstance(sample["labels"], list):
                errors.append(f"{prefix}: labels 必须是数组类型")
            elif not all(isinstance(label, str) for label in sample["labels"]):
                errors.append(f"{prefix}: labels 数组中的所有元素必须是字符串")

        return errors

    def _validate_sample_metadata(
        self,
        mode: SampleMode,
        metadata: Dict[str, Any],
        sample_index: int,
        image_width: Optional[int] = None,
        image_height: Optional[int] = None,
    ) -> List[str]:
        """验证样本元数据格式，返回详细错误信息"""
        errors = []
        prefix = f"样本 {sample_index} metadata"

        def validate_coordinate(
            coord,
            field_name: str,
            image_width: Optional[int] = None,
            image_height: Optional[int] = None,
            allow_point: bool = False,
        ) -> List[str]:
            """验证坐标格式

            Args:
                coord: 坐标数组
                field_name: 字段名称
                image_width: 图像宽度
                image_height: 图像高度
                allow_point: 是否允许点坐标 (x1=x2, y1=y2)，用于describe模式
            """
            coord_errors = []
            if not isinstance(coord, list):
                coord_errors.append(f"{prefix}.{field_name}: 必须是数组类型")
                return coord_errors

            if len(coord) != 4:
                coord_errors.append(
                    f"{prefix}.{field_name}: 必须包含4个元素 [x1, y1, x2, y2]"
                )
                return coord_errors

            # 允许全为null的情况
            if all(x is None for x in coord):
                return coord_errors

            # 否则必须都是整数
            for j, val in enumerate(coord):
                if not isinstance(val, int):
                    if isinstance(val, float):
                        coord_errors.append(
                            f"{prefix}.{field_name}[{j}]: 必须是整数，不能是浮点数"
                        )
                    else:
                        coord_errors.append(
                            f"{prefix}.{field_name}[{j}]: 必须是整数或null"
                        )

            # 如果没有数字格式错误，验证坐标顺序和范围
            if not coord_errors:
                x1, y1, x2, y2 = coord

                # 验证坐标顺序
                if allow_point:
                    # describe模式允许点坐标 (x1=x2, y1=y2)
                    if x1 > x2 or y1 > y2:
                        coord_errors.append(
                            f"{prefix}.{field_name}: 坐标顺序无效 (x1不能>x2, y1不能>y2)"
                        )
                else:
                    # 其他模式要求严格的矩形 (x1 < x2, y1 < y2)
                    if x1 >= x2 or y1 >= y2:
                        coord_errors.append(
                            f"{prefix}.{field_name}: 坐标顺序无效 (x1必须<x2, y1必须<y2)"
                        )

                # 如果提供了图像尺寸，验证坐标是否在有效范围内
                if image_width is not None and image_height is not None:
                    if not (
                        0 <= x1 <= image_width
                        and 0 <= x2 <= image_width
                        and 0 <= y1 <= image_height
                        and 0 <= y2 <= image_height
                    ):
                        coord_errors.append(
                            f"{prefix}.{field_name}: 坐标超出图像范围 (0-{image_width}, 0-{image_height})"
                        )

            return coord_errors

        try:
            if mode == SampleMode.GROUNDING:
                # Grounding模式: {description: str, coordinate: [x1,y1,x2,y2]}
                required_keys = {"description", "coordinate"}
                missing_keys = required_keys - set(metadata.keys())
                if missing_keys:
                    errors.append(f"{prefix}: 缺少必需字段 {', '.join(missing_keys)}")

                if "description" in metadata:
                    if not isinstance(metadata["description"], str):
                        errors.append(f"{prefix}.description: 必须是字符串类型")
                    elif not metadata["description"].strip():
                        errors.append(f"{prefix}.description: 不能为空")

                if "coordinate" in metadata:
                    errors.extend(
                        validate_coordinate(
                            metadata["coordinate"],
                            "coordinate",
                            image_width,
                            image_height,
                        )
                    )

            elif mode == SampleMode.DESCRIBE:
                # Describe模式: {coordinate: [x1,y1,x2,y2], description: str}
                # 注意：describe模式使用点坐标，允许 x1=x2, y1=y2
                required_keys = {"coordinate", "description"}
                missing_keys = required_keys - set(metadata.keys())
                if missing_keys:
                    errors.append(f"{prefix}: 缺少必需字段 {', '.join(missing_keys)}")

                if "description" in metadata:
                    if not isinstance(metadata["description"], str):
                        errors.append(f"{prefix}.description: 必须是字符串类型")
                    elif not metadata["description"].strip():
                        errors.append(f"{prefix}.description: 不能为空")

                if "coordinate" in metadata:
                    errors.extend(
                        validate_coordinate(
                            metadata["coordinate"],
                            "coordinate",
                            image_width,
                            image_height,
                            allow_point=True,
                        )
                    )

            elif mode == SampleMode.ENUMERATE_TEXT:
                # Enumerate Text模式: {general_description: str, elements: [str, ...]}
                required_keys = {"general_description", "elements"}
                missing_keys = required_keys - set(metadata.keys())
                if missing_keys:
                    errors.append(f"{prefix}: 缺少必需字段 {', '.join(missing_keys)}")

                if "general_description" in metadata:
                    if not isinstance(metadata["general_description"], str):
                        errors.append(f"{prefix}.general_description: 必须是字符串类型")

                if "elements" in metadata:
                    if not isinstance(metadata["elements"], list):
                        errors.append(f"{prefix}.elements: 必须是数组类型")
                    else:
                        for j, elem in enumerate(metadata["elements"]):
                            if not isinstance(elem, str):
                                errors.append(
                                    f"{prefix}.elements[{j}]: 必须是字符串类型"
                                )

            elif mode == SampleMode.ENUMERATE_COORD:
                # Enumerate Coord模式: {general_description: str, elements: [[x1,y1,x2,y2], ...]}
                required_keys = {"general_description", "elements"}
                missing_keys = required_keys - set(metadata.keys())
                if missing_keys:
                    errors.append(f"{prefix}: 缺少必需字段 {', '.join(missing_keys)}")

                if "general_description" in metadata:
                    if not isinstance(metadata["general_description"], str):
                        errors.append(f"{prefix}.general_description: 必须是字符串类型")

                if "elements" in metadata:
                    if not isinstance(metadata["elements"], list):
                        errors.append(f"{prefix}.elements: 必须是数组类型")
                    else:
                        for j, elem in enumerate(metadata["elements"]):
                            coord_errors = validate_coordinate(
                                elem, f"elements[{j}]", image_width, image_height
                            )
                            errors.extend(coord_errors)

            elif mode == SampleMode.CHECKLIST:
                # Checklist模式: {descriptions: [str, ...], results: [bool, ...]}
                required_keys = {"descriptions", "results"}
                missing_keys = required_keys - set(metadata.keys())
                if missing_keys:
                    errors.append(f"{prefix}: 缺少必需字段 {', '.join(missing_keys)}")

                descriptions = metadata.get("descriptions")
                results = metadata.get("results")

                if descriptions is not None:
                    if not isinstance(descriptions, list):
                        errors.append(f"{prefix}.descriptions: 必须是数组类型")
                    else:
                        for j, desc in enumerate(descriptions):
                            if not isinstance(desc, str):
                                errors.append(
                                    f"{prefix}.descriptions[{j}]: 必须是字符串类型"
                                )

                if results is not None:
                    if not isinstance(results, list):
                        errors.append(f"{prefix}.results: 必须是数组类型")
                    else:
                        for j, result in enumerate(results):
                            if not isinstance(result, bool):
                                errors.append(f"{prefix}.results[{j}]: 必须是布尔类型")

                # 检查长度是否匹配
                if (
                    descriptions is not None
                    and results is not None
                    and isinstance(descriptions, list)
                    and isinstance(results, list)
                ):
                    if len(descriptions) != len(results):
                        errors.append(
                            f"{prefix}: descriptions 和 results 数组长度必须相同 "
                            f"(descriptions: {len(descriptions)}, results: {len(results)})"
                        )

            elif mode == SampleMode.ENSURE:
                # Ensure模式: {expected_state: str, coordinate: [x1,y1,x2,y2]}
                required_keys = {"expected_state", "coordinate"}
                missing_keys = required_keys - set(metadata.keys())
                if missing_keys:
                    errors.append(f"{prefix}: 缺少必需字段 {', '.join(missing_keys)}")

                if "expected_state" in metadata:
                    if not isinstance(metadata["expected_state"], str):
                        errors.append(f"{prefix}.expected_state: 必须是字符串类型")
                    elif not metadata["expected_state"].strip():
                        errors.append(f"{prefix}.expected_state: 不能为空")

                if "coordinate" in metadata:
                    errors.extend(
                        validate_coordinate(
                            metadata["coordinate"],
                            "coordinate",
                            image_width,
                            image_height,
                        )
                    )

        except Exception as e:
            errors.append(f"{prefix}: 验证时出现异常 - {str(e)}")

        return errors

    def process_image_batch(
        self,
        image_base_path: Path,
        image_paths: List[str],
        progress_callback: Optional[Callable[[int, int], None]] = None,
        use_symlinks: Optional[bool] = None,
    ) -> Tuple[Dict[str, Image], List[str]]:
        """
        批量处理图片文件

        使用多线程进行文件操作（复制、哈希计算、验证），但数据库操作保持单线程

        Args:
            image_base_path: 图片基础路径
            image_paths: 图片相对路径列表
            progress_callback: 进度回调函数，接收(current, total)参数
            use_symlinks: 是否使用软链接，None时使用配置默认值

        Returns:
            Tuple: (成功处理的图片字典, 错误信息列表)
        """
        import shutil
        from concurrent.futures import ThreadPoolExecutor, as_completed

        from ..config import settings

        processed_images = {}
        errors = []

        def process_single_image_file(
            image_path: str,
        ) -> Tuple[str, Optional[Dict], Optional[str]]:
            """
            处理单个图片文件（仅文件操作，不涉及数据库）
            返回(路径, 图片信息字典或None, 错误信息或None)
            """
            try:
                full_path = image_base_path / image_path
                if not full_path.exists():
                    return image_path, None, f"图片文件不存在: {image_path}"

                # 验证图片格式
                if not self.image_service.validate_image_format(full_path):
                    return image_path, None, f"不支持的图片格式或文件过大: {image_path}"

                # 计算哈希值
                hash_value = self.image_service.calculate_image_hash(full_path)

                # 获取图片信息
                width, height, file_size = self.image_service.get_image_info(full_path)

                # 组织目标路径
                target_path = self.image_service.organize_image_path(
                    hash_value, image_path
                )

                # 复制或链接文件
                should_use_symlinks = (
                    use_symlinks if use_symlinks is not None else settings.USE_SYMLINKS
                )

                # 检查目标文件是否已存在（避免重复复制）
                if not target_path.exists():
                    if should_use_symlinks:
                        # 使用绝对路径创建软链接
                        source_abs_path = full_path.resolve()
                        try:
                            target_path.symlink_to(source_abs_path)
                        except OSError as e:
                            return (
                                image_path,
                                None,
                                f"创建软链接失败 {image_path}: {str(e)}",
                            )
                    else:
                        # 复制文件到目标位置
                        shutil.copy2(full_path, target_path)

                # 计算相对路径（相对于storage根目录）
                relative_path = target_path.relative_to(settings.STORAGE_ROOT)

                # 返回图片信息，稍后用于数据库操作
                image_info = {
                    "hash_value": hash_value,
                    "file_path": str(relative_path),
                    "file_size": file_size,
                    "width": width,
                    "height": height,
                    "original_path": image_path,
                }

                return image_path, image_info, None

            except Exception as e:
                return image_path, None, f"处理图片失败 {image_path}: {str(e)}"

        # 如果图片数量较少，使用串行处理避免线程开销
        if len(image_paths) <= 5:
            # 串行处理文件操作
            file_results = []
            for i, image_path in enumerate(image_paths):
                path, image_info, error = process_single_image_file(image_path)
                file_results.append((path, image_info, error))

                # 更新进度（文件处理阶段）
                if progress_callback:
                    progress_callback(i + 1, len(image_paths))
        else:
            # 使用多线程并行处理文件操作
            max_workers = min(settings.IMPORT_MAX_WORKERS, len(image_paths))
            file_results = []

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有文件处理任务
                future_to_path = {
                    executor.submit(process_single_image_file, image_path): image_path
                    for image_path in image_paths
                }

                # 收集文件处理结果
                completed = 0
                for future in as_completed(future_to_path):
                    image_path = future_to_path[future]
                    try:
                        path, image_info, error = future.result()
                        file_results.append((path, image_info, error))

                        completed += 1
                        # 更新进度（文件处理阶段）
                        if progress_callback:
                            progress_callback(completed, len(image_paths))

                    except Exception as e:
                        error_msg = f"线程执行错误 {image_path}: {str(e)}"
                        file_results.append((image_path, None, error_msg))
                        completed += 1
                        if progress_callback:
                            progress_callback(completed, len(image_paths))

        # 单线程处理数据库操作
        # 计算需要处理的有效图片数量（用于进度跟踪）
        valid_images = [
            item for item in file_results if item[1] is not None and item[2] is None
        ]
        db_processed = 0

        for path, image_info, error in file_results:
            if error:
                errors.append(error)
            elif image_info:
                try:
                    # 检查数据库中是否已存在相同哈希的图片
                    existing_image = self.image_service.check_duplicate_image(
                        image_info["hash_value"]
                    )
                    if existing_image:
                        # 如果图片已存在，直接使用现有记录
                        processed_images[path] = existing_image
                    else:
                        # 创建新的数据库记录
                        from ..models import Image

                        image = Image(
                            hash_value=image_info["hash_value"],
                            file_path=image_info["file_path"],
                            file_size=image_info["file_size"],
                            width=image_info["width"],
                            height=image_info["height"],
                        )

                        self.db.add(image)
                        self.db.commit()
                        self.db.refresh(image)
                        processed_images[path] = image

                    # 更新数据库操作进度
                    db_processed += 1
                    if progress_callback and len(valid_images) > 5:
                        # 对于多线程处理的情况，提供数据库操作的进度反馈
                        # 使用总数 + 当前数据库进度来区分两个阶段
                        progress_callback(
                            len(image_paths) + db_processed,
                            len(image_paths) + len(valid_images),
                        )

                except Exception as e:
                    error_msg = f"数据库操作失败 {path}: {str(e)}"
                    errors.append(error_msg)
                    # 如果数据库操作失败，尝试清理已复制的文件
                    try:
                        target_path = settings.STORAGE_ROOT / image_info["file_path"]
                        if target_path.exists():
                            target_path.unlink()
                    except Exception as _:
                        pass  # 清理失败不影响主流程

                    # 即使失败也要更新进度
                    db_processed += 1
                    if progress_callback and len(valid_images) > 5:
                        progress_callback(
                            len(image_paths) + db_processed,
                            len(image_paths) + len(valid_images),
                        )

        return processed_images, errors

    def process_image_batch_mapreduce(
        self,
        image_base_path: Path,
        image_paths: List[str],
        progress_callback: Optional[Callable[[int, int], None]] = None,
        use_symlinks: Optional[bool] = None,
        chunk_size: int = 1000,
    ) -> Tuple[Dict[str, Image], List[str]]:
        """
        使用Map-Reduce模式批量处理图片文件，适用于大规模数据集

        Args:
            image_base_path: 图片基础路径
            image_paths: 图片相对路径列表
            progress_callback: 进度回调函数，接收(current, total)参数
            use_symlinks: 是否使用软链接，None时使用配置默认值
            chunk_size: 每个处理块的大小

        Returns:
            Tuple: (成功处理的图片字典, 错误信息列表)
        """

        processed_images = {}
        all_errors = []
        total_images = len(image_paths)
        processed_count = 0

        # 分块处理
        for i in range(0, total_images, chunk_size):
            chunk = image_paths[i : i + chunk_size]

            # Map阶段：并行处理文件操作
            chunk_file_results = self._map_process_files(
                chunk, image_base_path, use_symlinks
            )

            # Reduce阶段：聚合和去重
            chunk_image_data_and_mapping = self._reduce_prepare_image_data(
                chunk_file_results
            )

            # Commit阶段：批量数据库操作
            chunk_images, chunk_errors = self._commit_images_batch(
                chunk_image_data_and_mapping, chunk
            )

            # 合并结果
            processed_images.update(chunk_images)
            all_errors.extend(chunk_errors)

            # 更新进度
            processed_count += len(chunk)
            if progress_callback:
                progress_callback(min(processed_count, total_images), total_images)

        return processed_images, all_errors

    def _map_process_files(
        self,
        image_paths: List[str],
        image_base_path: Path,
        use_symlinks: Optional[bool],
    ) -> List[Tuple[str, Optional[Dict], Optional[str]]]:
        """
        Map阶段：并行处理文件操作
        """
        import shutil
        from concurrent.futures import ThreadPoolExecutor, as_completed

        from ..config import settings

        def process_single_image_file(
            image_path: str,
        ) -> Tuple[str, Optional[Dict], Optional[str]]:
            """处理单个图片文件（仅文件操作，不涉及数据库）"""
            try:
                full_path = image_base_path / image_path
                if not full_path.exists():
                    return image_path, None, f"图片文件不存在: {image_path}"

                # 验证图片格式
                if not self.image_service.validate_image_format(full_path):
                    return image_path, None, f"不支持的图片格式或文件过大: {image_path}"

                # 计算哈希值
                hash_value = self.image_service.calculate_image_hash(full_path)

                # 获取图片信息
                width, height, file_size = self.image_service.get_image_info(full_path)

                # 组织目标路径
                target_path = self.image_service.organize_image_path(
                    hash_value, image_path
                )

                # 复制或链接文件
                should_use_symlinks = (
                    use_symlinks if use_symlinks is not None else settings.USE_SYMLINKS
                )

                if should_use_symlinks:
                    # 创建软链接
                    if not target_path.exists():
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        target_path.symlink_to(full_path.resolve())
                else:
                    # 复制文件
                    if not target_path.exists():
                        target_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(full_path, target_path)

                return (
                    image_path,
                    {
                        "hash_value": hash_value,
                        "file_path": str(
                            target_path.relative_to(settings.STORAGE_ROOT)
                        ),
                        "file_size": file_size,
                        "width": width,
                        "height": height,
                    },
                    None,
                )

            except Exception as e:
                return image_path, None, f"处理图片文件失败 {image_path}: {str(e)}"

        # 并行处理文件
        if len(image_paths) <= 5:
            # 小批量使用串行处理
            return [process_single_image_file(path) for path in image_paths]
        else:
            # 大批量使用并行处理
            max_workers = min(settings.IMPORT_MAX_WORKERS, len(image_paths))
            results = []

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_path = {
                    executor.submit(process_single_image_file, path): path
                    for path in image_paths
                }

                for future in as_completed(future_to_path):
                    results.append(future.result())

            return results

    def _reduce_prepare_image_data(
        self, file_results: List[Tuple[str, Optional[Dict], Optional[str]]]
    ) -> Tuple[List[Dict], Dict[str, str]]:
        """
        Reduce阶段：聚合和去重图片数据

        Returns:
            Tuple[List[Dict], Dict[str, str]]: (去重的图片数据列表, 路径到哈希的映射)
        """
        # 过滤出成功处理的图片数据
        valid_image_data = []
        seen_hashes = set()
        path_to_hash = {}

        for path, image_info, error in file_results:
            if error or not image_info:
                continue

            hash_value = image_info["hash_value"]
            path_to_hash[path] = hash_value

            # 去重：同一个哈希值只保留第一个
            if hash_value not in seen_hashes:
                seen_hashes.add(hash_value)
                valid_image_data.append(image_info)

        return valid_image_data, path_to_hash

    def _commit_images_batch(
        self,
        image_data_and_mapping: Tuple[List[Dict], Dict[str, str]],
        original_paths: List[str],
    ) -> Tuple[Dict[str, Image], List[str]]:
        """
        Commit阶段：批量数据库操作
        """
        image_data_list, path_to_hash = image_data_and_mapping

        if not image_data_list:
            return {}, []

        # 使用新的批量创建方法
        created_images, errors = self.image_service.bulk_create_images(
            image_data_list, commit=True
        )

        # 获取所有相关的图片（包括已存在的）
        all_hashes = list(path_to_hash.values())
        all_images = self.image_service.get_images_by_hashes_batch(all_hashes)

        # 构建路径到图片的映射
        path_to_image = {}
        for path in original_paths:
            if path in path_to_hash:
                hash_value = path_to_hash[path]
                if hash_value in all_images:
                    path_to_image[path] = all_images[hash_value]

        return path_to_image, errors

    def import_batch_from_json_mapreduce(
        self,
        json_file_path: Path,
        image_base_path: Optional[Path] = None,
        image_progress_callback: Optional[Callable[[int, int], None]] = None,
        sample_progress_callback: Optional[Callable[[int, int], None]] = None,
        use_symlinks: Optional[bool] = None,
        chunk_size: int = 1000,
    ) -> Tuple[Batch, List[Sample], List[str]]:
        """
        使用Map-Reduce模式从JSON文件导入批次数据，适用于大规模数据集

        Args:
            json_file_path: JSON文件路径
            image_base_path: 图片基础路径，如果为None则使用JSON文件所在目录
            image_progress_callback: 图片处理进度回调函数，接收(current, total)参数
            sample_progress_callback: 样本创建进度回调函数，接收(current, total)参数
            use_symlinks: 是否使用软链接，None时使用配置默认值
            chunk_size: 每个处理块的大小

        Returns:
            Tuple: (批次记录, 样本列表, 错误信息列表)
        """
        # 解析JSON数据
        data = self.parse_import_json(json_file_path)

        # 设置图片基础路径
        if image_base_path is None:
            image_base_path = json_file_path.parent

        all_errors = []

        try:
            # 创建批次
            batch_data = BatchCreate(
                name=data["batch_name"], description=data.get("batch_description", "")
            )
            batch = self.batch_service.create_batch(batch_data)

            # 收集所有图片路径
            image_paths = [sample["image_path"] for sample in data["samples"]]

            # 使用Map-Reduce模式批量处理图片
            processed_images, image_errors = self.process_image_batch_mapreduce(
                image_base_path,
                image_paths,
                image_progress_callback,
                use_symlinks,
                chunk_size,
            )

            # 添加图片处理错误到总错误列表
            all_errors.extend(image_errors)

            # 准备样本创建数据（只为成功处理的图片准备样本）
            samples_to_create = []
            for sample_data in data["samples"]:
                image_path = sample_data["image_path"]

                # 检查图片是否成功处理
                if image_path in processed_images:
                    image = processed_images[image_path]

                    # 处理JSON中的"metadata"字段映射到"sample_metadata"
                    sample_metadata = sample_data.get("metadata", sample_data.get("sample_metadata", {}))

                    sample_create = SampleCreate(
                        image_id=image.id,
                        batch_id=batch.id,
                        mode=SampleMode(sample_data["mode"]),
                        sample_metadata=sample_metadata,
                        labels=sample_data.get("labels", []),
                    )
                    samples_to_create.append(sample_create)

            # 使用流式批量创建样本
            created_samples, sample_errors = (
                self.sample_service.create_samples_streaming(
                    samples_to_create, chunk_size, sample_progress_callback
                )
            )

            # 添加样本创建错误到总错误列表
            all_errors.extend(sample_errors)

            # 如果没有任何样本成功创建，删除空的批次
            if not created_samples:
                self.batch_service.delete_batch(batch.id)
                raise ValueError(
                    f"导入失败：没有样本被成功创建。错误数量: {len(all_errors)}"
                )

            return batch, created_samples, all_errors

        except Exception as e:
            # 如果有严重错误，回滚事务
            self.db.rollback()
            raise ValueError(f"导入失败: {str(e)}")

    def _cleanup_failed_images(self, processed_images: Dict[str, Image]) -> None:
        """清理导入失败时已复制的图片文件和数据库记录"""
        for image_path, image in processed_images.items():
            try:
                # 删除物理文件（使用存储路径，不解析软链接）
                storage_path = self.image_service.get_storage_path(image)
                if storage_path.exists():
                    # 安全删除：如果是软链接，只删除链接本身，不删除目标文件
                    storage_path.unlink()

                # 删除数据库记录（如果存在）
                # 首先检查image是否已经在数据库中持久化
                if image.id:
                    # 检查是否有其他样本引用此图片
                    if not image.samples:
                        # 如果没有样本引用，删除数据库记录
                        self.db.delete(image)

            except Exception as e:
                # 记录清理错误但不影响主流程
                print(f"Warning: Failed to cleanup image {image_path}: {e}")

    def _cleanup_orphaned_images(self) -> int:
        """清理孤立的图片记录（没有关联样本的图片）"""
        from sqlalchemy import text

        # 查找没有关联样本的图片
        orphaned_images = self.db.execute(
            text("""
            SELECT i.* FROM images i 
            LEFT JOIN samples s ON i.id = s.image_id 
            WHERE s.id IS NULL
        """)
        ).fetchall()

        cleaned_count = 0
        for row in orphaned_images:
            try:
                # 重新获取Image对象
                image = self.db.query(Image).filter(Image.id == row.id).first()
                if image:
                    # 删除物理文件（使用存储路径，不解析软链接）
                    storage_path = self.image_service.get_storage_path(image)
                    if storage_path.exists():
                        # 安全删除：如果是软链接，只删除链接本身，不删除目标文件
                        storage_path.unlink()

                    # 删除数据库记录
                    self.db.delete(image)
                    cleaned_count += 1
            except Exception as e:
                print(f"Warning: Failed to cleanup orphaned image {row.id}: {e}")

        if cleaned_count > 0:
            self.db.commit()

        return cleaned_count

    def import_batch_from_json(
        self,
        json_file_path: Path,
        image_base_path: Optional[Path] = None,
        image_progress_callback: Optional[Callable[[int, int], None]] = None,
        sample_progress_callback: Optional[Callable[[int, int], None]] = None,
        use_symlinks: Optional[bool] = None,
    ) -> Tuple[Batch, List[Sample], List[str]]:
        """
        从JSON文件导入批次数据

        Args:
            json_file_path: JSON文件路径
            image_base_path: 图片基础路径，如果为None则使用JSON文件所在目录
            image_progress_callback: 图片处理进度回调函数，接收(current, total)参数
            sample_progress_callback: 样本创建进度回调函数，接收(current, total)参数
            use_symlinks: 是否使用软链接，None时使用配置默认值

        Returns:
            Tuple: (创建的批次, 创建的样本列表, 错误信息列表)
        """
        # 解析JSON数据
        data = self.parse_import_json(json_file_path)

        # 验证数据格式
        is_valid, validation_errors = self.validate_import_data(data)
        if not is_valid:
            error_msg = "导入数据格式无效:\n" + "\n".join(validation_errors[:10])
            if len(validation_errors) > 10:
                error_msg += f"\n... 还有 {len(validation_errors) - 10} 个错误"
            raise ValueError(error_msg)

        # 设置图片基础路径
        if image_base_path is None:
            image_base_path = json_file_path.parent

        all_errors = []

        try:
            # 创建批次
            batch_data = BatchCreate(
                name=data["batch_name"], description=data.get("batch_description", "")
            )
            batch = self.batch_service.create_batch(batch_data)

            # 收集所有图片路径
            image_paths = [sample["image_path"] for sample in data["samples"]]

            # 批量处理图片（容错模式）
            processed_images, image_errors = self.process_image_batch(
                image_base_path,
                image_paths,
                image_progress_callback,
                use_symlinks,
            )

            # 添加图片处理错误到总错误列表
            all_errors.extend(image_errors)

            # 准备样本创建数据（只为成功处理的图片准备样本）
            samples_to_create = []
            for sample_data in data["samples"]:
                image_path = sample_data["image_path"]

                # 检查图片是否成功处理
                if image_path in processed_images:
                    image = processed_images[image_path]

                    # 处理JSON中的"metadata"字段映射到"sample_metadata"
                    sample_metadata = sample_data.get("metadata", sample_data.get("sample_metadata", {}))

                    sample_create = SampleCreate(
                        image_id=image.id,
                        batch_id=batch.id,
                        mode=SampleMode(sample_data["mode"]),
                        sample_metadata=sample_metadata,
                        labels=sample_data.get("labels", []),
                    )
                    samples_to_create.append(sample_create)

            # 批量创建样本
            created_samples, sample_errors = self.sample_service.create_samples_batch(
                samples_to_create, sample_progress_callback
            )

            # 添加样本创建错误到总错误列表
            all_errors.extend(sample_errors)

            # 如果没有任何样本成功创建，删除空的批次
            if not created_samples:
                self.batch_service.delete_batch(batch.id)
                raise ValueError(
                    f"导入失败：没有样本被成功创建。错误数量: {len(all_errors)}"
                )

            return batch, created_samples, all_errors

        except Exception as e:
            # 如果有严重错误，回滚事务
            self.db.rollback()
            raise ValueError(f"导入失败: {str(e)}")

    def handle_import_errors(self, errors: List[str]) -> Dict[str, Any]:
        """处理导入过程中的错误"""
        return {
            "error_count": len(errors),
            "errors": errors,
            "severity": "warning" if errors else "none",
        }

    def get_import_summary(
        self, batch: Batch, samples: List[Sample], errors: List[str]
    ) -> Dict[str, Any]:
        """生成导入摘要"""
        mode_counts = {}
        for sample in samples:
            mode = sample.mode.value
            mode_counts[mode] = mode_counts.get(mode, 0) + 1

        return {
            "batch_id": batch.id,
            "batch_name": batch.name,
            "total_samples": len(samples),
            "mode_distribution": mode_counts,
            "errors": self.handle_import_errors(errors),
            "success": len(errors) == 0,
        }
