"""
Parallel Database Manager for PostgreSQL-optimized operations
"""

import threading
from contextlib import contextmanager
from typing import Any, Callable, Dict, Generator, List, Optional, TypeVar

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from ..config import settings

T = TypeVar("T")


class ParallelDatabaseManager:
    """
    管理并行数据库操作的连接池和会话

    为PostgreSQL提供真正的并行处理能力，为其他数据库提供顺序处理回退
    """

    def __init__(self):
        self._engines: Dict[str, Any] = {}
        self._session_factories: Dict[str, Any] = {}
        self._lock = threading.Lock()

    def _get_engine_for_worker(self, worker_id: str):
        """为工作线程获取专用的数据库引擎"""
        with self._lock:
            if worker_id not in self._engines:
                if "postgresql" in settings.DATABASE_URL:
                    # PostgreSQL: 为每个工作线程创建独立的引擎
                    self._engines[worker_id] = create_engine(
                        settings.DATABASE_URL,
                        pool_pre_ping=True,
                        pool_recycle=3600,
                        pool_size=5,  # 每个工作线程的小连接池
                        max_overflow=10,
                    )
                else:
                    # SQLite等其他数据库：使用共享引擎
                    from ..database import engine

                    self._engines[worker_id] = engine

                # 创建会话工厂
                self._session_factories[worker_id] = sessionmaker(
                    autocommit=False, autoflush=False, bind=self._engines[worker_id]
                )

            return self._engines[worker_id], self._session_factories[worker_id]

    @contextmanager
    def get_worker_session(self, worker_id: str) -> Generator[Session, None, None]:
        """为工作线程获取数据库会话"""
        engine, session_factory = self._get_engine_for_worker(worker_id)
        session = session_factory()
        try:
            yield session
        finally:
            session.close()

    def is_parallel_supported(self) -> bool:
        """检查是否支持并行数据库操作"""
        return "postgresql" in settings.DATABASE_URL

    def execute_parallel(
        self,
        tasks: List[T],
        worker_func: Callable[[T, Session], Any],
        max_workers: Optional[int] = None,
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> List[Any]:
        """
        并行执行数据库任务

        Args:
            tasks: 任务列表
            worker_func: 工作函数，接收(task, session)参数
            max_workers: 最大工作线程数
            progress_callback: 进度回调函数

        Returns:
            List[Any]: 执行结果列表
        """
        if not self.is_parallel_supported() or len(tasks) <= 1:
            # 回退到顺序处理
            return self._execute_sequential(tasks, worker_func, progress_callback)

        return self._execute_parallel_postgresql(
            tasks, worker_func, max_workers, progress_callback
        )

    def _execute_sequential(
        self,
        tasks: List[T],
        worker_func: Callable[[T, Session], Any],
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> List[Any]:
        """顺序执行任务（SQLite等数据库的回退方案）"""
        from ..database import SessionLocal

        results = []
        session = SessionLocal()

        try:
            for i, task in enumerate(tasks):
                try:
                    result = worker_func(task, session)
                    results.append(result)

                    if progress_callback:
                        progress_callback(i + 1, len(tasks))

                except Exception as e:
                    results.append({"error": str(e), "task": task})

        finally:
            session.close()

        return results

    def _execute_parallel_postgresql(
        self,
        tasks: List[T],
        worker_func: Callable[[T, Session], Any],
        max_workers: Optional[int] = None,
        progress_callback: Optional[Callable[[int, int], None]] = None,
    ) -> List[Any]:
        """PostgreSQL并行执行任务"""
        from concurrent.futures import ThreadPoolExecutor, as_completed

        if max_workers is None:
            max_workers = min(
                len(tasks), settings.IMPORT_MAX_WORKERS // 4
            )  # 保守的并行度

        results: List[Any] = [None] * len(tasks)
        completed_count = 0

        def worker_wrapper(task_with_index):
            task_index, task = task_with_index
            worker_id = f"worker_{threading.current_thread().ident}"

            with self.get_worker_session(worker_id) as session:
                try:
                    return task_index, worker_func(task, session)
                except Exception as e:
                    return task_index, {"error": str(e), "task": task}

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(worker_wrapper, (i, task)): i
                for i, task in enumerate(tasks)
            }

            # 收集结果
            for future in as_completed(future_to_index):
                task_index, result = future.result()
                results[task_index] = result

                completed_count += 1
                if progress_callback:
                    progress_callback(completed_count, len(tasks))

        return results

    def cleanup(self):
        """清理资源"""
        with self._lock:
            for engine in self._engines.values():
                if hasattr(engine, "dispose"):
                    engine.dispose()
            self._engines.clear()
            self._session_factories.clear()


# 全局实例
parallel_db_manager = ParallelDatabaseManager()
