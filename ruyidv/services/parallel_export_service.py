"""
Parallel Export Service with PostgreSQL optimization
"""

from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple

from rich.console import Console
from sqlalchemy.orm import Session

from ..models import Sample, SampleMode
from .export_service import ExportService
from .parallel_db_manager import parallel_db_manager
from .sample_service import SampleService


class ParallelExportService:
    """
    并行导出服务，针对PostgreSQL优化，其他数据库使用顺序处理
    """

    def __init__(self, db: Session):
        self.db = db
        self.sample_service = SampleService(db)
        self.export_service = ExportService(db)

    def export_parallel(
        self,
        batch_ids: Optional[List[int]] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
        formatter: Optional[str] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        chunk_size: int = 1000,
        console: Optional[Console] = None,
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        并行导出数据，针对PostgreSQL优化

        Args:
            batch_ids: 批次ID列表过滤
            modes: 模式列表过滤
            labels: 标签过滤
            formatter: 自定义格式化器名称
            limit: 导出数量限制
            offset: 偏移量
            copy_images: 是否复制图片到导出目录
            export_dir: 导出目录
            image_subdir: 图片子目录名
            chunk_size: 处理块大小
            console: Rich Console对象

        Returns:
            Tuple: (导出数据列表, 错误信息列表)
        """
        if not parallel_db_manager.is_parallel_supported():
            # 回退到原有的map-reduce实现
            return self.export_service.export_with_filters_mapreduce(
                batch_ids=batch_ids,
                modes=modes,
                labels=labels,
                formatter=formatter,
                limit=limit,
                offset=offset,
                copy_images=copy_images,
                export_dir=export_dir,
                image_subdir=image_subdir,
                chunk_size=chunk_size,
                console=console,
            )

        # PostgreSQL并行处理
        if console is None:
            console = Console()

        console.print("[blue]🚀 Using PostgreSQL parallel export...[/blue]")

        # 获取样本总数
        total_count = self.sample_service.count_samples_with_filters(
            batch_ids=batch_ids, modes=modes, labels=labels
        )

        if limit is not None:
            actual_export_count = min(limit, total_count - offset)
        else:
            actual_export_count = total_count - offset

        if actual_export_count <= 0:
            return [], []

        console.print(f"[blue]📊 Total samples to export: {actual_export_count}[/blue]")

        # 分块并行处理
        all_formatted_data = []
        all_errors = []
        processed_count = 0

        for i in range(0, actual_export_count, chunk_size):
            current_chunk_size = min(chunk_size, actual_export_count - processed_count)
            current_offset = offset + processed_count

            console.print(
                f"[blue]🔄 Processing chunk {i // chunk_size + 1} "
                f"({current_chunk_size} samples)...[/blue]"
            )

            # 并行处理当前块
            chunk_data, chunk_errors = self._process_export_chunk_parallel(
                batch_ids=batch_ids,
                modes=modes,
                labels=labels,
                formatter=formatter,
                limit=current_chunk_size,
                offset=current_offset,
                copy_images=copy_images,
                export_dir=export_dir,
                image_subdir=image_subdir,
                console=console,
            )

            all_formatted_data.extend(chunk_data)
            all_errors.extend(chunk_errors)
            processed_count += current_chunk_size

            console.print(
                f"[green]✅ Chunk completed: {processed_count}/{actual_export_count}[/green]"
            )

        return all_formatted_data, all_errors

    def _process_export_chunk_parallel(
        self,
        batch_ids: Optional[List[int]] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
        formatter: Optional[str] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        console: Optional[Console] = None,
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """并行处理导出块"""

        # 获取样本数据
        samples = self.sample_service.get_samples_with_filters(
            batch_ids=batch_ids,
            modes=modes,
            labels=labels,
            limit=limit,
            offset=offset,
        )

        if not samples:
            return [], []

        # 并行处理样本
        def process_sample_worker(sample: Sample, session: Session) -> Dict:
            try:
                # 格式化样本数据为ShareGPT格式
                formatted_data = self._format_sample_data(sample, formatter, session)

                # 处理图片路径
                if copy_images and export_dir:
                    # 检查是否有图像处理器
                    if "image_processor" in formatted_data:
                        # 有图像处理器，需要处理图像
                        image_path = self._process_sample_image_with_processor(
                            sample, export_dir, image_subdir, formatted_data["image_processor"], session
                        )
                        # 移除image_processor，不应该在最终输出中
                        del formatted_data["image_processor"]
                    else:
                        # 没有图像处理器，常规复制
                        image_path = self._process_sample_image(
                            sample, export_dir, image_subdir, session
                        )

                    if image_path:
                        formatted_data["images"] = [image_path]
                    else:
                        formatted_data["images"] = []
                else:
                    # 使用绝对路径
                    absolute_path = self.export_service.image_service.get_absolute_path(sample.image)
                    formatted_data["images"] = [str(absolute_path)]
                    # 移除image_processor，不应该在最终输出中
                    if "image_processor" in formatted_data:
                        del formatted_data["image_processor"]

                return {"data": formatted_data, "success": True}

            except Exception as e:
                return {"error": str(e), "sample_id": sample.id, "success": False}

        # 执行并行任务，带进度条
        from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, MofNCompleteColumn, TimeRemainingColumn

        if console:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                MofNCompleteColumn(),
                TimeRemainingColumn(),
                console=console,
            ) as progress:
                task = progress.add_task("Processing samples...", total=len(samples))

                def progress_callback(current: int, _total: int) -> None:
                    progress.update(task, completed=current)

                results = parallel_db_manager.execute_parallel(
                    samples, process_sample_worker, max_workers=8, progress_callback=progress_callback
                )
        else:
            results = parallel_db_manager.execute_parallel(
                samples, process_sample_worker, max_workers=8
            )

        # 处理结果
        formatted_data = []
        errors = []

        for result in results:
            if result["success"]:
                formatted_data.append(result["data"])
            else:
                errors.append(
                    f"Sample {result.get('sample_id', 'unknown')}: {result['error']}"
                )

        return formatted_data, errors

    def _format_sample_data(
        self, sample: Sample, formatter: Optional[str], _session: Session
    ) -> Dict[str, Any]:
        """格式化样本数据为ShareGPT格式"""
        try:
            # 获取格式化器函数
            formatter_func = None
            if formatter:
                from . import get_formatter
                formatter_func = get_formatter(formatter)
                if not formatter_func:
                    raise ValueError(f"Custom formatter '{formatter}' not found")
            else:
                # 使用默认格式化器
                formatter_func = self.export_service.get_default_formatter(sample.mode)

            # 检查metadata类型，如果是list则转换为适当的dict格式
            metadata = sample.sample_metadata
            if isinstance(metadata, list):
                # 对于enumerate_coord模式，如果metadata是list，假设它是elements列表
                if sample.mode == SampleMode.ENUMERATE_COORD:
                    metadata = {
                        "general_description": "",
                        "elements": metadata
                    }
                else:
                    # 对于其他模式，创建一个基本的dict结构
                    metadata = {
                        "description": "",
                        "elements": metadata if isinstance(metadata, list) else []
                    }

            # 如果metadata需要修改，临时修改sample的metadata
            original_metadata = sample.sample_metadata
            if metadata != original_metadata:
                sample.sample_metadata = metadata

            try:
                # 应用格式化器
                formatted = self.export_service.apply_custom_formatter(sample, formatter_func)
            finally:
                # 恢复原始metadata
                if metadata != original_metadata:
                    sample.sample_metadata = original_metadata

            # 返回ShareGPT格式，包含可能的图像处理器
            result = {
                "instruction": formatted["instruction"],
                "input": "",  # 根据设计要求为空
                "output": formatted["output"],
            }

            # 如果有图像处理器，也包含在结果中
            if "image_processor" in formatted:
                result["image_processor"] = formatted["image_processor"]

            return result

        except Exception as e:
            # 如果格式化失败，返回详细错误信息
            metadata_type = type(sample.sample_metadata).__name__
            metadata_preview = str(sample.sample_metadata)[:100] + "..." if len(str(sample.sample_metadata)) > 100 else str(sample.sample_metadata)

            return {
                "instruction": f"Error formatting sample {sample.id} (mode: {sample.mode.value})",
                "input": "",
                "output": f"Formatting error: {str(e)} | Metadata type: {metadata_type} | Preview: {metadata_preview}",
                "formatter_error": str(e)
            }



    def _process_sample_image(
        self, sample: Sample, export_dir: Path, image_subdir: str, _session: Session
    ) -> Optional[str]:
        """处理样本图片复制"""
        try:
            import shutil

            from ..config import settings

            # 获取源图片路径
            source_path = settings.STORAGE_ROOT / sample.image.file_path
            if not source_path.exists():
                return None

            # 构建目标路径
            target_dir = export_dir / image_subdir
            target_dir.mkdir(parents=True, exist_ok=True)

            # 使用哈希值作为文件名
            file_extension = source_path.suffix
            target_filename = f"{sample.image.hash_value}{file_extension}"
            target_path = target_dir / target_filename

            # 复制文件（如果不存在）
            if not target_path.exists():
                shutil.copy2(source_path, target_path)

            # 返回相对路径
            return f"{image_subdir}/{target_filename}"

        except Exception:
            # 记录错误但不中断处理
            return None

    def _process_sample_image_with_processor(
        self, sample: Sample, export_dir: Path, image_subdir: str,
        image_processor: Callable, _session: Session
    ) -> Optional[str]:
        """处理样本图片复制并应用图像处理器"""
        try:
            # 获取源文件路径
            source_path = self.export_service.image_service.get_absolute_path(sample.image)

            if not source_path.exists():
                return None

            # 创建图片目录
            images_dir = export_dir / image_subdir
            images_dir.mkdir(parents=True, exist_ok=True)

            # 解析软链接获取真实文件
            real_source_path = (
                source_path.resolve()
                if source_path.is_symlink()
                else source_path
            )

            # 处理图像
            processed_image = self.export_service.process_image_with_formatter(
                real_source_path, image_processor
            )

            if processed_image is None:
                return None

            # 生成目标文件名 - 使用样本特定的文件名以避免冲突
            original_extension = source_path.suffix or ".jpg"
            target_filename = f"{sample.image.hash_value}_sample_{sample.id}_processed{original_extension}"
            target_path = images_dir / target_filename

            # 防止文件名冲突的安全检查
            counter = 1
            while target_path.exists():
                target_filename = f"{sample.image.hash_value}_sample_{sample.id}_processed_{counter}{original_extension}"
                target_path = images_dir / target_filename
                counter += 1

            # 保存处理后的图像
            processed_image.save(target_path, quality=95, optimize=True)

            # 返回相对路径
            return str(Path(image_subdir) / target_filename)

        except Exception:
            return None

    def export_with_split_parallel(
        self,
        batch_ids: Optional[List[int]] = None,
        modes: Optional[List[SampleMode]] = None,
        labels: Optional[List[str]] = None,
        formatter: Optional[str] = None,
        limit: Optional[int] = None,
        offset: int = 0,
        copy_images: bool = False,
        export_dir: Optional[Path] = None,
        image_subdir: str = "images",
        split_ratios: List[int] = [8, 1, 1],
        random_seed: Optional[int] = None,
        chunk_size: int = 1000,
        console: Optional[Console] = None,
    ) -> Tuple[Dict[str, List[Dict[str, Any]]], List[str]]:
        """
        并行导出数据并进行分层抽样划分
        """
        if not parallel_db_manager.is_parallel_supported():
            # 回退到原有实现
            return self.export_service.export_with_split_mapreduce(
                batch_ids=batch_ids,
                modes=modes,
                labels=labels,
                formatter=formatter,
                limit=limit,
                offset=offset,
                copy_images=copy_images,
                export_dir=export_dir,
                image_subdir=image_subdir,
                split_ratios=split_ratios,
                random_seed=random_seed,
                chunk_size=chunk_size,
                console=console,
            )

        # PostgreSQL并行处理
        if console is None:
            console = Console()

        # 首先导出所有数据
        all_data, errors = self.export_parallel(
            batch_ids=batch_ids,
            modes=modes,
            labels=labels,
            formatter=formatter,
            limit=limit,
            offset=offset,
            copy_images=copy_images,
            export_dir=export_dir,
            image_subdir=image_subdir,
            chunk_size=chunk_size,
            console=console,
        )

        if not all_data:
            return {"train": [], "val": [], "test": []}, errors

        # 进行分层抽样划分
        console.print("[blue]🔄 Performing stratified split...[/blue]")

        # 重用现有的分层抽样逻辑
        # 这里需要实现分层抽样逻辑
        # 为了简化，暂时平均分配
        total_samples = len(all_data)
        train_size = int(total_samples * split_ratios[0] / sum(split_ratios))
        val_size = int(total_samples * split_ratios[1] / sum(split_ratios))

        result = {
            "train": all_data[:train_size],
            "val": all_data[train_size : train_size + val_size],
            "test": all_data[train_size + val_size :],
        }

        return result, errors
